import { PrismaClient, Role, User } from "@prisma/client";
import crypto from "crypto";
import bcrypt from "bcryptjs";
import { generateToken, verifyToken } from "../utils/jwtUtils";
import { AppError } from "../utils/ApiError";
import { generateOtp } from "../utils/otp";
import { createPasswordResetToken } from "../utils/generate-token";
import { mailSender } from "../utils/mailSender";
import { dodoAxios } from "../lib/axiosDodo";
import { paypalTemplate } from "../templates/paypal";
import { EnhancedEmailService } from "./email/enhanced-email.service";
import paypalClient2 from "../lib/axiosPaypal2";
import { AuthenticatedRequest } from "../middlewares/checkAuth";

const prisma = new PrismaClient();

export class ClientAuthService {
  private emailService: EnhancedEmailService;

  constructor() {
    this.emailService = new EnhancedEmailService();
  }
  /**
   * Clean up unverified user and all related records
   * This method handles the proper deletion of incomplete registrations
   */
  private async cleanupUnverifiedUser(userId: string) {
    try {
      // Get user details for external service cleanup before transaction
      const user = await prisma.user.findUnique({
        where: { id: userId },
        select: {
          id: true,
          email: true,
          customer_id: true,
          business_id: true,
          emailVerified: true,
        },
      });

      if (!user) {
        return; // User doesn't exist, nothing to clean up
      }

      // Only clean up if user is not verified
      if (user.emailVerified) {
        throw new Error("Cannot delete verified user");
      }

      console.log(`Cleaning up unverified user: ${user.email}`);

      // Clean up external services first (before transaction)
      if (user.customer_id) {
        try {
          await dodoAxios.delete(`customers/${user.customer_id}`);
          console.log(`Deleted Dodo customer: ${user.customer_id}`);
        } catch (error) {
          console.warn(
            `Failed to delete Dodo customer ${user.customer_id}:`,
            error
          );
          // Continue with cleanup even if external service fails
        }
      }

      // Use a transaction to ensure all database deletions succeed or fail together
      await prisma.$transaction(async (tx) => {
        // Delete related records in the correct order (children first, then parent)

        // 1. Delete authentication related records
        await tx.authSession.deleteMany({ where: { userId } });
        await tx.userSessions.deleteMany({ where: { userId } });
        await tx.token.deleteMany({ where: { userId } });
        await tx.restrictedToken.deleteMany({ where: { userId } });

        // 2. Delete profile and settings
        await tx.profile.deleteMany({ where: { userId } });
        await tx.userProfile.deleteMany({ where: { userId } });
        await tx.userSettings.deleteMany({ where: { userId } });
        await tx.billingAddress.deleteMany({ where: { userId } });

        // 3. Delete activity and notification records
        await tx.userActivity.deleteMany({ where: { userId } });
        await tx.userActivityLog.deleteMany({ where: { userId } });
        await tx.userNotification.deleteMany({ where: { userId } });
        await tx.userFeedback.deleteMany({ where: { userId } });

        // 4. Delete subscription related records
        const subscriptions = await tx.subscription.findMany({
          where: { userId },
          select: { id: true },
        });

        for (const subscription of subscriptions) {
          await tx.payment.deleteMany({
            where: { subscriptionId: subscription.id },
          });
        }
        await tx.subscription.deleteMany({ where: { userId } });

        // 5. Delete package and assignment related records
        await tx.clientPackageDetails.deleteMany({
          where: { clientId: userId },
        });
        await tx.clientPackageDetails.deleteMany({
          where: { assignedAnnotatorId: userId },
        });
        await tx.assignment.deleteMany({ where: { clientId: userId } });
        await tx.assignment.deleteMany({ where: { developerId: userId } });
        await tx.assignment.deleteMany({ where: { coordinatorId: userId } });

        // 6. Delete attendance and time tracking records
        await tx.breakSession.deleteMany({ where: { userId } });
        await tx.attendanceSummary.deleteMany({ where: { userId } });
        await tx.timeLog.deleteMany({ where: { userId } });

        // 7. Delete shift and leave requests
        await tx.shiftChangeRequest.deleteMany({
          where: { annotatorId: userId },
        });
        await tx.shiftChangeRequest.deleteMany({
          where: { requestedById: userId },
        });
        await tx.shiftChangeRequest.deleteMany({
          where: { approvedById: userId },
        });
        await tx.leaveRequest.deleteMany({ where: { annotatorId: userId } });
        await tx.leaveRequest.deleteMany({ where: { approvedById: userId } });

        // 8. Delete messaging related records
        await tx.mention.deleteMany({ where: { userId } });
        await tx.reaction.deleteMany({ where: { userId } });
        await tx.messageReadStatus.deleteMany({ where: { userId } });
        await tx.groupMember.deleteMany({ where: { userId } });
        await tx.conversationParticipant.deleteMany({ where: { userId } });

        // Delete messages sent and received
        await tx.message.deleteMany({ where: { senderId: userId } });
        await tx.message.deleteMany({ where: { receiverId: userId } });

        // 9. Delete project and task related records
        // First, remove user from task annotators (many-to-many)
        const tasksWithUser = await tx.task.findMany({
          where: { annotators: { some: { id: userId } } },
          select: { id: true },
        });

        for (const task of tasksWithUser) {
          await tx.task.update({
            where: { id: task.id },
            data: { annotators: { disconnect: { id: userId } } },
          });
        }

        // Remove user from project annotators (many-to-many)
        const projectsWithUser = await tx.project.findMany({
          where: { annotators: { some: { id: userId } } },
          select: { id: true },
        });

        for (const project of projectsWithUser) {
          await tx.project.update({
            where: { id: project.id },
            data: { annotators: { disconnect: { id: userId } } },
          });
        }

        // Delete project coordinator assignments
        await tx.projectCoordinatorAssignment.deleteMany({
          where: { projectCoordinatorId: userId },
        });

        // Delete tasks, self tasks, and admin tasks created by user
        await tx.task.deleteMany({ where: { createdById: userId } });
        await tx.selfTask.deleteMany({ where: { createdById: userId } });
        await tx.adminTask.deleteMany({ where: { createdById: userId } });
        await tx.adminTask.deleteMany({ where: { assignedToId: userId } });

        // Delete projects owned by user
        await tx.project.deleteMany({ where: { createdById: userId } });

        // 10. Delete partner related records
        await tx.partnerUser.deleteMany({ where: { userId } });

        // 11. Delete notifications
        await tx.notification.deleteMany({ where: { userId } });

        // 12. Delete user role and permission records
        await tx.userRole.deleteMany({ where: { userId } });
        await tx.userPermission.deleteMany({ where: { userId } });

        // 13. Delete coworker relationships
        await tx.user.updateMany({
          where: { clientOwnerId: userId },
          data: { clientOwnerId: null },
        });

        // 14. Finally, delete the user record
        await tx.user.delete({ where: { id: userId } });

        console.log(`Successfully cleaned up unverified user: ${user.email}`);
      });
    } catch (error) {
      console.error("Error cleaning up unverified user:", error);
      throw new Error(
        "Failed to clean up previous registration. Please try again."
      );
    }
  }

  /**
   * Clean up expired OTP records for unverified users
   * This method can be called periodically to clean up stale registrations
   */
  async cleanupExpiredOtpUsers() {
    try {
      const now = new Date();

      // Find users with expired OTPs who are not verified
      const expiredUsers = await prisma.user.findMany({
        where: {
          emailVerified: null,
          otpExpiry: {
            lt: now,
          },
        },
        select: {
          id: true,
          email: true,
          otpExpiry: true,
        },
      });

      console.log(`Found ${expiredUsers.length} users with expired OTPs`);

      for (const user of expiredUsers) {
        // Only clean up users whose OTP expired more than 24 hours ago
        const expiredHoursAgo =
          (now.getTime() - user.otpExpiry!.getTime()) / (1000 * 60 * 60);

        if (expiredHoursAgo > 24) {
          console.log(`Cleaning up user with expired OTP: ${user.email}`);
          await this.cleanupUnverifiedUser(user.id);
        }
      }

      return {
        message: `Cleaned up ${expiredUsers.length} expired OTP users`,
        cleanedCount: expiredUsers.length,
      };
    } catch (error) {
      console.error("Error cleaning up expired OTP users:", error);
      throw new Error("Failed to clean up expired OTP users");
    }
  }
  // async registerClientUser({
  //   name,
  //   email,
  //   password,
  //   role = Role.CLIENT,
  // }: {
  //   name: string;
  //   email: string;
  //   password: string;
  //   role?: Role;
  // }) {
  //   const hashedPassword = await bcrypt.hash(password, 10);

  //   const user = await prisma.user.create({
  //     data: {
  //       name,
  //       email,
  //       passwordHash: hashedPassword,
  //       role,
  //     },
  //   });

  //   return user;
  // }

  // registerClientUser

  async registerClientUser({
    name,
    email,
    password,
    role = Role.CLIENT,
  }: {
    name: string;
    email: string;
    password: string;
    role?: Role;
  }) {
    let dodoCustomer: any = null;
    const existingUser = await prisma.user.findUnique({
      where: { email },
    });

    if (existingUser) {
      if (existingUser.emailVerified) {
        throw new Error("User already exists with this email");
      }

      // Clean up unverified user and all related records
      await this.cleanupUnverifiedUser(existingUser.id);
    }
    const hashedPassword = await bcrypt.hash(password, 10);

    // Generate OTP
    const { otp, otpExpiry } = await generateOtp();

    if (role === Role.CLIENT) {
      try {
        // Create customer in Dodo Payments
        dodoCustomer = await dodoAxios.post("customers", {
          email,
          name,
        });
        console.log(dodoCustomer, "Dodo customer created");
      } catch (err) {
        console.error("Error creating Dodo customer:", err);
        throw new Error("Failed to create customer in billing system");
      }
    }

    const user = await prisma.user.create({
      data: {
        name,
        email,
        passwordHash: hashedPassword,
        role,
        otpCode: otp,
        otpExpiry: otpExpiry,
        customer_id: dodoCustomer?.data.customer_id,
        business_id: dodoCustomer?.data.business_id,
      },
    });

    // Send OTP Email using enhanced email service
    await this.emailService.sendEmail({
      to: user.email,
      subject: `Welcome to ${process.env.COMPANY_NAME || "Our Platform"
        } - Verify Your Email`,
      template: "otp-signup",
      data: {
        firstName: user.name,
        otpCode: otp,
        timeLimit: 10, // 10 minutes
        companyName: process.env.COMPANY_NAME || "Our Platform",
        supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
        phoneNumber: process.env.SUPPORT_PHONE || "******-567-8900",
        websiteUrl: process.env.FRONTEND_URL || "https://company.com",
      },
    });

    return {
      message:
        "User registered successfully. Please verify OTP sent to your email.",
    };
  }

  // Verify OTP
  async verifyRegisterOtp({ email, otp }: { email: string; otp: string }) {
    const user: any = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) {
      throw new Error("User not found");
    }

    if (!user.otpCode || !user.otpExpiry) {
      throw new Error("No OTP requested for this user");
    }

    if (user.otpCode !== otp) {
      throw new Error("Invalid OTP");
    }

    if (new Date(user.otpExpiry) < new Date()) {
      throw new Error("OTP expired");
    }

    await prisma.user.update({
      where: { email },
      data: {
        emailVerified: new Date(),
        otpCode: null,
        otpExpiry: null,
      },
    });

    const token = await this.generateLoginToken(user.id, user.role);

    return { message: "Email verified successfully.", token };
  }

  // New method to save profile details
  async saveProfileDetails(
    userId: string,
    {
      companyName,
      phoneNumber,
      website,
      timezone,
      country,
      stateProvince,
      address,
      postalCode,
    }: {
      companyName: string;
      phoneNumber: string;
      website: string;
      timezone: string;
      country: string;
      stateProvince: string;
      address: string;
      postalCode: string;
    }
  ) {
    try {
      const updatedUser = await prisma.user.update({
        where: { id: userId },
        data: {
          timezone,
          domain: website,
        },
      });

      const updatedProfile = await prisma.profile.upsert({
        where: { userId },
        update: {
          companyName,
          phoneNumber,
          website,
          address,
          postalCode,
          country,
          stateProvince,
        },
        create: {
          userId,
          companyName,
          phoneNumber,
          website,
          address,
          postalCode,
          country,
          stateProvince,
        },
      });

      return {
        message: "Profile details saved successfully",
        user: updatedUser,
        profile: updatedProfile,
      };
    } catch (error) {
      console.error("Error saving profile details:", error);
      throw new Error("Failed to save profile details");
    }
  }

  async getProfileDetails(userId: string) {
    try {
      const user = await prisma.user.findUnique({
        where: { id: userId },
        include: {
          Profile: true,
        },
      });

      if (!user) {
        throw new Error("User not found");
      }

      return {
        message: "Profile details fetched successfully",
        user: {
          id: user.id,
          name: user.name,
          lastname: user.lastname,
          email: user.email,
          timezone: user.timezone,
          domain: user.domain,
        },
        profile: user.Profile,
      };
    } catch (error) {
      console.error("Error fetching profile details:", error);
      throw new Error("Failed to fetch profile details");
    }
  }

  async getUserProfileById(id: string) {
    const user = await prisma.user.findUnique({
      where: { id },
      include: {
        Profile: true,
      },
    });

    if (!user) return null;

    return {
      user: {
        id: user.id,
        name: user.name,
        lastname: user.lastname,
        email: user.email,
        timezone: user.timezone,
        domain: user.domain,
        role: user.role,
      },
      profile: user.Profile,
    };
  }

  // Paypa Subscription create
  async createPaypalSubscription(
    planId: string,
    user: User,
    subscriptionID: string,
    address: any
  ): Promise<string> {
    try {
      const response = await paypalClient2.post("/v1/billing/subscriptions", {
        plan_id: planId,
        custom_id: subscriptionID,
        subscriber: {
          name: {
            given_name: user.name,
            surname: user.lastname,
          },
          email_address: user.email,
          shipping_address: {
            name: {
              full_name: user.name,
            },
            address: {
              address_line_1: address.street,
              address_line_2: address.street,
              admin_area_2: address.city, // City
              admin_area_1: address.state, // State
              postal_code: address.zipcode,
              country_code: address.country,
            },
          },
        },
        application_context: {
          brand_name: "Your Company",
          locale: "en-US",
          shipping_preference: "NO_SHIPPING", // or "NO_SHIPPING" if you don't want to collect
          user_action: "SUBSCRIBE_NOW",
          return_url: `${process.env.RET_URL}paypal/success`,
          cancel_url: `${process.env.RET_URL}paypal/failed`,
        },
      });

      console.log(response, "reponse data");

      return (
        response.data.approve_link ||
        response.data.links?.find((link: any) => link.rel === "approve")?.href
      );
    } catch (error: any) {
      console.error(
        "PayPal Subscription Error:",
        error?.response?.data || error.message
      );
      throw new AppError("Failed to create PayPal subscription", 500);
    }
  }

  async updateClientDetails(userId: string, data: any) {
    const user = await prisma.user.findFirst({
      where: { id: userId },
    });

    const externalReference = crypto.randomUUID();
    let dodoSubscription: any;

    console.log(data, "data");

    if (data.packageId) {
      const existingPackage = await prisma.package.findUnique({
        where: { id: data.packageId },
      });
      if (!existingPackage) {
        throw new AppError("Package not found", 404);
      }

      // Create subscription
      const res = await prisma.subscription.create({
        data: {
          packageId: data.packageId,
          startDate: new Date(),
          endDate: new Date(new Date().setMonth(new Date().getMonth() + 1)),
          status: "PENDING",
          userId,
          externalReference: externalReference,
        },
      });
      // Create ClientPackageDetails record
      await prisma.clientPackageDetails.create({
        data: {
          clientId: userId,
          packageId: data.packageId,
          availableFrom: data.availableFrom,
          availableTo: data.availableTo,
          timezone: data.timezone,
          industry: data.industry,
          category: data.category,
          startOn: data.startOn ? new Date(data.startOn) : null,
          description: data.description ?? null,
          subscriptionId: res.id,
        },
      });

      // create or update address
      await prisma.billingAddress.upsert({
        where: { userId },
        update: {
          country: data.billing.country,
          address: data.billing.city,
          state: data.billing.state,
          postalCodel: data.billing.zipcode,
          street: data.billing.street,
        },
        create: {
          userId: userId,
          country: data.billing.country,
          address: data.billing.city,
          state: data.billing.state,
          postalCodel: data.billing.zipcode,
          street: data.billing.street,
        },
      });

      if (data.paymentMethod === "dodo") {
        // Create subscription in Dodo
        dodoSubscription = await dodoAxios.post("subscriptions", {
          billing: { country: "US",city: data.billing.city,
          state: data.billing.state,
         zipcode: data.billing.zipcode,
          street: data.billing.street },
          customer: { customer_id: user?.customer_id },
          payment_link: true,
          return_url: process.env.RET_URL,
          allowed_payment_method_types: ["credit", "debit"],
          product_id: existingPackage.product_id,
          quantity: 1,
          metadata: {
            externalReference: externalReference,
          },
        });
        console.log(dodoSubscription, "Dodo subscription created");
      } else if (data.paymentMethod === "paypal") {
        dodoSubscription = await this.createPaypalSubscription(
          existingPackage?.paypalPlanId!,
          user!,
          res?.id!,
          data.billing
        );
        console.log(dodoSubscription, "mangaread link");
        return { payment_link: dodoSubscription };
      } else if (data.paymentMethod === "other") {
        // Send email with the token
        const html = paypalTemplate
          .replace("{{customerName}}", user?.name ?? "Customer")
          .replace("{{packageCategory}}", existingPackage.name)
          .replace("{{timeZone}}", data.timezone)
          .replace("{{industry}}", data.timezone)
          .replace("{{fromTime}}", data.availableFrom)
          .replace("{{toTime}}", data.availableTo)
          .replace("{{notes}}", data.notes ?? "")
          .replace("{{amount}}", `${existingPackage?.price}`)
          .replace("{{paypalLink}}", process.env.PAYPALLINK ?? "");

        console.log(html, "html");

        await mailSender({
          user: {
            name: user?.name ?? "",
            email: user?.email ?? "",
            subject: "Payment Instructions via PayPal – GetAnnotator",
            body: html,
          },
        });
      }
    } else {
      throw new AppError("Please Select the Package", 400);
    }
    console.log(dodoSubscription, "dodoSubscription");
    return data.paymentMethod === "dodo"
      ? dodoSubscription.data
      : dodoSubscription;
  }

  async validateUserCredentials(email: string, password: string) {
    const user = await prisma.user.findFirst({
      where: {
        OR: [{ email: email }],
      },
    });

    console.log(user);

    if (!user) {
      throw new AppError("Invalid credentials", 400);
    }

    // Check if user account is deleted
    if (user.isDeleted) {
      throw new AppError(
        "Your account has been deleted. Please contact support.",
        403
      );
    }

    // Check if user account is suspended
    if (user.accountStatus === "SUSPENDED") {
      // Check if suspension has expired
      if (user.suspendedUntil && new Date() > user.suspendedUntil) {
        // Suspension has expired, reactivate the account
        await prisma.user.update({
          where: { id: user.id },
          data: {
            accountStatus: "ACTIVE",
            suspendedUntil: null,
          },
        });
      } else {
        // Account is still suspended
        const suspensionMessage = user.suspendedUntil
          ? `Your account is suspended until ${user.suspendedUntil.toLocaleDateString()}. Please contact support.`
          : "Your account has been suspended. Please contact support.";
        throw new AppError(suspensionMessage, 403);
      }
    }

    if (!user.passwordHash) {
      throw new Error("Password hash is missing");
    }

    const isPasswordValid = await bcrypt.compare(password, user.passwordHash);

    if (!isPasswordValid) {
      throw new AppError("Invalid credentials", 400);
    }

    return user;
  }

  async generateLoginToken(userId: string, userRole: string) {
    return generateToken({ userId, userRole });
  }

  async invalidateToken(token: string) {
    // You can implement token invalidation using a token blacklist stored in Redis or a database
    return true;
  }

  async checkAndUpdateRateLimit(
    userId: string,
    limit = 5,
    cooldownMs = 60 * 1000
  ) {
    const restrictedToken = await prisma.restrictedToken.findFirst({
      where: { userId },
    });

    const now = new Date();

    if (restrictedToken) {
      // If rate limit is exceeded
      if (restrictedToken.rateLimit <= 0 && now < restrictedToken.expires!) {
        throw new Error("Too many OTP requests. Try again later.");
      }

      // Reset rate limit if cooldown time expired
      if (now >= restrictedToken.expires!) {
        await prisma.restrictedToken.update({
          where: { id: restrictedToken.id },
          data: {
            rateLimit: limit, // Reset rate limit
            lastUsed: now,
            expires: new Date(now.getTime() + cooldownMs), // Cooldown period
          },
        });
      } else {
        // Decrement rate limit
        await prisma.restrictedToken.update({
          where: { id: restrictedToken.id },
          data: {
            rateLimit: { decrement: 1 },
            lastUsed: now,
          },
        });
      }
    } else {
      // Create a new rate limit entry
      await prisma.restrictedToken.create({
        data: {
          name: "OTP Request",
          hashedKey: crypto.randomBytes(32).toString("hex"),
          partialKey: "otp",
          scopes: "otp:send",
          expires: new Date(now.getTime() + cooldownMs),
          lastUsed: now,
          rateLimit: limit,
          userId,
          projectId: "default_project",
        },
      });
    }
  }

  async sendPasswordResetEmail(email: string) {
    // Implement email sending logic here
    const user = await prisma.user.findUnique({
      where: {
        email,
      },
    });

    if (!user) {
      throw new AppError("User not found", 404);
    }

    // Check if user is rate-limited
    // const restrictedToken = await prisma.restrictedToken.findFirst({
    //   where: { userId: user.id },
    // });

    // const now = new Date();

    // if (restrictedToken) {
    //   // If rate limit is exceeded
    //   if (restrictedToken.rateLimit <= 0 && now < restrictedToken.expires!) {
    //     throw new Error("Too many OTP requests. Try again later.");
    //   }

    //   // Reset rate limit if time expired
    //   if (now >= restrictedToken.expires!) {
    //     await prisma.restrictedToken.updateMany({
    //       where: { userId: user.id },
    //       data: {
    //         rateLimit: 5, // Reset rate limit
    //         lastUsed: now,
    //         expires: new Date(now.getTime() + 60 * 1000), // 1 min cooldown
    //       },
    //     });
    //   } else {
    //     // Decrement rate limit
    //     await prisma.restrictedToken.updateMany({
    //       where: { userId: user.id },
    //       data: {
    //         rateLimit: { decrement: 1 },
    //         lastUsed: now,
    //       },
    //     });
    //   }
    // } else {
    //   // Create a new rate limit entry
    //   await prisma.restrictedToken.create({
    //     data: {
    //       name: "OTP Request",
    //       hashedKey: crypto.randomBytes(32).toString("hex"), // Generate random key
    //       partialKey: "otp",
    //       scopes: "otp:send",
    //       expires: new Date(now.getTime() + 60 * 1000), // 1 min cooldown
    //       lastUsed: now,
    //       rateLimit: 5,
    //       userId: user.id,
    //       projectId: "default_project", // Change based on your setup
    //     },
    //   });
    // }
    await this.checkAndUpdateRateLimit(user.id);

    // Generate otp to verify user
    const response = await prisma.passwordResetToken.findFirst({
      where: {
        identifier: email,
      },
    });
    const { otp, otpExpiry } = await generateOtp();

    if (response) {
      await prisma.passwordResetToken.updateMany({
        where: {
          identifier: email,
        },
        data: {
          token: otp,
          expires: otpExpiry,
        },
      });
    } else {
      await prisma.passwordResetToken.create({
        data: {
          identifier: email,
          token: otp,
          expires: otpExpiry,
        },
      });
    }
    // Send email with OTP using enhanced email service
    await this.emailService.sendEmail({
      to: user.email,
      subject: "Password Reset Verification",
      template: "otp-verification",
      data: {
        customerName: user.name,
        otpCode: otp,
        timeLimit: 10, // 10 minutes
        companyName: process.env.COMPANY_NAME || "Our Platform",
        supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
        phoneNumber: process.env.SUPPORT_PHONE || "******-567-8900",
        website: process.env.FRONTEND_URL || "https://company.com",
      },
    });

    console.log(`Sending password reset email to ${email}`);
    return {
      message: "Password reset email sent",
      otp,
    };
  }

  async verifyOtp(email: string, otp: string) {
    const token = await prisma.passwordResetToken.findFirst({
      where: {
        identifier: email,
      },
    });

    if (!token) {
      throw new AppError("Invalid or expired OTP", 400);
    }

    if (new Date(token.expires) < new Date()) {
      throw new AppError("OTP expired", 400);
    }

    if (token.token !== otp) {
      throw new AppError("Invalid OTP", 400);
    }
    // OTP is valid, proceed with password reset
    // You can also delete the OTP token after successful verification
    await prisma.passwordResetToken.delete({
      where: {
        identifier_token: {
          identifier: email,
          token: token.token, // Since it's stored as a hashed value, use token.token
        },
      },
    });
    // Generate a new password reset token

    const newToken = await createPasswordResetToken();
    await prisma.passwordResetToken.create({
      data: {
        identifier: email,
        token: newToken.token,
        expires: newToken.expiresAt,
      },
    });

    return newToken;
  }

  async resendOtp(email: string) {
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) throw new AppError("User not found", 404);

    await this.checkAndUpdateRateLimit(user.id);

    const isRegistered = !!user.emailVerified;
    const now = new Date();

    const { otp, otpExpiry } = await generateOtp();

    await prisma.user.update({
      where: { email },
      data: {
        otpCode: otp,
        otpExpiry: otpExpiry,
      },
    });

    const subject = isRegistered ? "Login OTP" : "Verify your email";
    const body = isRegistered
      ? ` Use this OTP to login: ${otp}. It is valid for 10 minutes.`
      : `Thanks for registering! Your OTP is ${otp}. It is valid for 10 minutes.`;

    await mailSender({
      user: {
        name: user.name || "",
        email: user.email,
        subject,
        body,
      },
    });

    return { message: "OTP resent successfully." };
  }

  async resendPasswordResetOtp(email: string) {
    const user = await prisma.user.findUnique({
      where: { email },
    });

    if (!user) throw new AppError("User not found", 404);

    await this.checkAndUpdateRateLimit(user.id);

    const { otp, otpExpiry } = await generateOtp();

    // Update or create password reset token
    const response = await prisma.passwordResetToken.findFirst({
      where: {
        identifier: email,
      },
    });

    if (response) {
      await prisma.passwordResetToken.updateMany({
        where: {
          identifier: email,
        },
        data: {
          token: otp,
          expires: otpExpiry,
        },
      });
    } else {
      await prisma.passwordResetToken.create({
        data: {
          identifier: email,
          token: otp,
          expires: otpExpiry,
        },
      });
    }

    // Send email with OTP using enhanced email service
    await this.emailService.sendEmail({
      to: user.email,
      subject: "Password Reset Verification",
      template: "otp-verification",
      data: {
        customerName: user.name,
        otpCode: otp,
        timeLimit: 10, // 10 minutes
        companyName: process.env.COMPANY_NAME || "Our Platform",
        supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
        phoneNumber: process.env.SUPPORT_PHONE || "******-567-8900",
        website: process.env.FRONTEND_URL || "https://company.com",
      },
    });

    return { message: "Password reset OTP resent successfully." };
  }

  async resetPassword(email: string, resetToken: string, newPassword: string) {
    const token = await prisma.passwordResetToken.findFirst({
      where: {
        identifier: email,
      },
    });

    if (!token) {
      throw new AppError("Invalid or expired reset token", 400);
    }

    if (new Date(token.expires) < new Date()) {
      throw new AppError("Reset token expired", 400);
    }

    // For client auth service, we use plain text comparison (OTP flow)
    // For onboarding service, hashed tokens are used
    if (token.token !== resetToken) {
      throw new AppError("Invalid reset token", 400);
    }
    // Reset the password
    // Hash the new password
    if (!newPassword) {
      throw new AppError("New password is required", 400);
    }
    const hashedPassword = await bcrypt.hash(newPassword, 10);

    const user = await prisma.user.update({
      where: {
        email,
      },
      data: {
        passwordHash: hashedPassword,
      },
    });

    await prisma.passwordResetToken.delete({
      where: {
        identifier_token: {
          identifier: email,
          token: token.token, // Since it's stored as a hashed value, use token.token
        },
      },
    });

    return user;
  }

  async getUserById(userId: string) {
    const user = await prisma.user.findUnique({
      where: {
        id: userId,
      },
      select: {
        id: true,
        name: true,
        lastname: true,
        email: true,
        role: true,
        availableFrom: true,
        availableTo: true,
        coworkerPermission: true,
      },
    });

    if (!user) {
      throw new AppError("User not found", 404);
    }

    // Check if user has any active subscription/plan
    let hasPlan = false;
    let hasPendingBankTransfer = false;

    try {
      // Only check subscriptions for CLIENT users
      if (user.role === "CLIENT") {
        // Check for active subscriptions
        const activeSubscriptions = await prisma.subscription.findMany({
          where: {
            userId: userId,
            status: {
              in: ["ACTIVE"], // Only check ACTIVE subscriptions
            },
            OR: [
              { endDate: null }, // No end date means active
              { endDate: { gte: new Date() } }, // End date is in the future
            ],
          },
        });

        // Check for pending bank transfer payments
        const pendingBankTransfers = await prisma.bankTransferPayment.findMany({
          where: {
            userId: userId,
            status: "PENDING",
          },
          orderBy: {
            createdAt: "desc", // Get the latest pending bank transfer
          },
        });

        // Check if user has any verified bank transfer payments
        const verifiedBankTransfers = await prisma.bankTransferPayment.findMany({
          where: {
            userId: userId,
            status: "VERIFIED",
          },
          include: {
            subscription: true,
          },
        });

        // Determine bank-transfer flag logic
        if (pendingBankTransfers.length > 0) {
          // If there are pending bank transfers, show bank-transfer = true
          hasPendingBankTransfer = true;
        } else if (verifiedBankTransfers.length > 0) {
          // If there are verified bank transfers but no pending ones, show bank-transfer = false
          hasPendingBankTransfer = false;
        } else {
          // No bank transfers at all, show bank-transfer = false
          hasPendingBankTransfer = false;
        }

        // Determine plan status
        if (activeSubscriptions.length > 0) {
          hasPlan = true;
        } else {
          // If no active subscriptions found, check for verified bank transfer payments
          for (const bankTransfer of verifiedBankTransfers) {
            if (bankTransfer.subscription &&
              bankTransfer.subscription.status === "ACTIVE" &&
              (!bankTransfer.subscription.endDate || bankTransfer.subscription.endDate >= new Date())) {
              hasPlan = true;
              break;
            }
          }
        }
      }
      // For all other roles (ANNOTATOR, COORDINATOR, ADMIN, COWORKER), return false
      else {
        hasPlan = false;
        hasPendingBankTransfer = false;
      }
    } catch (error) {
      console.error("Error checking user plan status:", error);
      // If there's an error checking plans, default to false for safety
      hasPlan = false;
      hasPendingBankTransfer = false;
    }

    return {
      ...user,
      plan: hasPlan,
      "bank-transfer": hasPendingBankTransfer,
    };
  }

  async changePassword(
    req: AuthenticatedRequest,
    userId: string,
    oldPassword: string,
    newPassword: string,
    confirmPassword: string
  ) {
    const user = await prisma.user.findUnique({
      where: { id: userId },
    });

    if (!user || !user.passwordHash) {
      throw new Error("User not found or password not set");
    }

    const isMatch = await bcrypt.compare(oldPassword, user.passwordHash);
    if (!isMatch) {
      throw new Error("Old password is incorrect");
    }

    if (newPassword !== confirmPassword) {
      throw new Error("New password and confirm password do not match");
    }

    const hashedNewPassword = await bcrypt.hash(newPassword, 10);

    await prisma.user.update({
      where: { id: userId },
      data: { passwordHash: hashedNewPassword },
    });

    await this.emailService.sendEmail({
      to: user.email,
      subject: `Password Changed Successfully`,
      template: "password-changed",
      data: {
        firstName: user.name,
        platformName: process.env.COMPANY_NAME || "Our Platform",
        email: user.email,
        dateTime: new Date(),
        ipAddress: req.ip || req.headers["x-forwarded-for"] || "Unknown",
        supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
        websiteUrl: process.env.FRONTEND_URL || "https://company.com",
      },
    });

    return { message: "Password changed successfully" };
  }

  async sendDeleteOtp(userId: string) {
    const user = await prisma.user.findUnique({ where: { id: userId } });

    if (!user) {
      throw new Error("User not found");
    }

    const { otp, otpExpiry } = await generateOtp();

    await prisma.user.update({
      where: { id: userId },
      data: {
        otpCode: otp,
        otpExpiry,
      },
    });

    await this.emailService.sendEmail({
      to: user.email,
      subject: "Account Deletion Verification",
      template: "otp-verification",
      data: {
        customerName: user.name || "User",
        otpCode: otp,
        timeLimit: 10, // 10 minutes
        companyName: process.env.COMPANY_NAME || "Our Platform",
        supportEmail: process.env.SUPPORT_EMAIL || "<EMAIL>",
        phoneNumber: process.env.SUPPORT_PHONE || "******-567-8900",
        website: process.env.FRONTEND_URL || "https://company.com",
      },
    });

    return { message: "OTP sent to your email for account deletion" };
  }

  async verifyOtpAndDelete(userId: string, otp: string) {
    const user = await prisma.user.findUnique({ where: { id: userId } });

    if (!user) {
      throw new Error("User not found");
    }

    if (!user.otpCode || !user.otpExpiry) {
      throw new Error("OTP not generated");
    }

    if (user.otpExpiry < new Date()) {
      throw new Error("OTP expired");
    }

    if (user.otpCode !== otp) {
      throw new Error("Invalid OTP");
    }

    await prisma.user.update({
      where: { id: userId },
      data: {
        isDeleted: true,
        otpCode: null,
        otpExpiry: null,
      },
    });

    return { message: "Account deleted successfully" };
  }

  async getUserBillingAddress(userId: string) {
    try {
      const billingAddress = await prisma.billingAddress.findUnique({
        where: {
          userId: userId,
        },
        select: {
          id: true,
          country: true,
          address: true,
          state: true,
          postalCodel: true,
          street: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return billingAddress;
    } catch (error: any) {
      console.error("Error fetching billing address:", error);
      throw new Error("Failed to fetch billing address");
    }
  }

  async createOrUpdateBillingAddress(
    userId: string,
    addressData: {
      country: string;
      address: string;
      state: string;
      postalCodel: string;
      street: string;
    }
  ) {
    try {
      const billingAddress = await prisma.billingAddress.upsert({
        where: {
          userId: userId,
        },
        update: {
          country: addressData.country,
          address: addressData.address,
          state: addressData.state,
          postalCodel: addressData.postalCodel,
          street: addressData.street,
        },
        create: {
          userId: userId,
          country: addressData.country,
          address: addressData.address,
          state: addressData.state,
          postalCodel: addressData.postalCodel,
          street: addressData.street,
        },
        select: {
          id: true,
          country: true,
          address: true,
          state: true,
          postalCodel: true,
          street: true,
          user: {
            select: {
              id: true,
              name: true,
              email: true,
            },
          },
        },
      });

      return billingAddress;
    } catch (error: any) {
      console.error("Error creating/updating billing address:", error);
      throw new Error("Failed to create or update billing address");
    }
  }
}
